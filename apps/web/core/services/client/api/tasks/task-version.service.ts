import {
	ITaskVersion,
	ITaskVersionCreateRequest,
	ITaskVersionUpdateRequest
} from '@/core/types/interfaces/task/task-version';
import { APIService } from '../../api.service';
import { GAUZY_API_BASE_SERVER_URL } from '@/core/constants/config/constants';
import { PaginationResponse } from '@/core/types/interfaces/common/data-response';
import { getActiveTeamIdCookie, getOrganizationIdCookie, getTenantIdCookie } from '@/core/lib/helpers/cookies';
import qs from 'qs';

class TaskVersionService extends APIService {
	get organizationId() {
		return getOrganizationIdCookie();
	}
	get tenantId() {
		return getTenantIdCookie();
	}
	get activeTeamId() {
		return getActiveTeamIdCookie();
	}

	get activeTeamBasedQueries() {
		return {
			'where[organizationTeamId]': this.activeTeamId,
			'where[organizationId]': this.organizationId,
			'where[tenantId]': this.tenantId
		};
	}

	createTaskVersion = async (data: ITaskVersionCreateRequest) => {
		return this.post<ITaskVersion>(`/task-versions`, data, { tenantId: this.tenantId });
	};

	updateTaskVersion = async (id: string, data: ITaskVersionUpdateRequest) => {
		return this.put<ITaskVersion>(`/task-versions/${id}`, data, { tenantId: this.tenantId });
	};

	deleteTaskVersion = async (id: string) => {
		return this.delete<ITaskVersion>(`/task-versions/${id}`, { tenantId: this.tenantId });
	};

	getTaskVersions = async () => {
		const query = qs.stringify(this.activeTeamBasedQueries);

		const endpoint = `/task-versions?${query}`;

		return this.get<PaginationResponse<ITaskVersion>>(endpoint, { tenantId: this.tenantId });
	};
}

export const taskVersionService = new TaskVersionService(GAUZY_API_BASE_SERVER_URL.value);
